
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.9';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// 模拟AI新闻数据源（实际项目中可以接入真实的新闻API）
const mockAINews = [
  {
    title: "Meta发布新一代AI芯片架构",
    summary: "Meta公司发布了专为AI训练优化的新芯片架构，性能提升3倍",
    content: "Meta在其开发者大会上正式发布了新一代AI芯片架构MTIA v2，该芯片专为大语言模型训练和推理优化...",
    author: "Meta AI团队",
    source: "Meta官方",
    category: "Hardware",
    tags: ["Meta", "AI芯片", "硬件", "MTIA"]
  },
  {
    title: "Google推出Gemini Ultra突破性更新",
    summary: "Google Gemini Ultra在多项基准测试中超越GPT-4，展现出强大的多模态能力",
    content: "Google最新发布的Gemini Ultra模型在MMLU、GSM8K等多项基准测试中取得了超越GPT-4的成绩...",
    author: "Google DeepMind",
    source: "Google AI",
    category: "Language Model",
    tags: ["Google", "Gemini", "多模态", "基准测试"]
  },
  {
    title: "AI在药物发现领域取得重大突破",
    summary: "最新AI系统成功预测了新型抗生素结构，有望解决抗药性问题",
    content: "麻省理工学院和哈佛大学的研究团队利用深度学习模型成功发现了一种全新的抗生素分子...",
    author: "MIT研究团队",
    source: "Nature Medicine",
    category: "Healthcare",
    tags: ["药物发现", "抗生素", "深度学习", "医疗AI"]
  },
  {
    title: "OpenAI发布新版ChatGPT企业解决方案",
    summary: "新版本提供更强的企业级安全和定制化功能",
    content: "OpenAI今日宣布推出ChatGPT Enterprise 2.0，新增了企业级数据隔离、自定义模型训练等功能...",
    author: "OpenAI团队",
    source: "OpenAI官方",
    category: "Enterprise AI",
    tags: ["OpenAI", "ChatGPT", "企业版", "安全"]
  }
];

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    // 使用服务角色密钥来绕过RLS限制
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    console.log('开始获取AI新闻...');
    
    // 获取最近24小时内的新闻，避免重复插入
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const { data: existingNews } = await supabase
      .from('ai_news')
      .select('title')
      .gte('created_at', yesterday.toISOString());
    
    const existingTitles = new Set(existingNews?.map(news => news.title) || []);
    
    // 过滤掉已存在的新闻
    const newNewsItems = mockAINews.filter(news => !existingTitles.has(news.title));
    
    if (newNewsItems.length === 0) {
      console.log('没有新的AI新闻需要添加');
      return new Response(JSON.stringify({ 
        message: '没有新的AI新闻', 
        newsCount: 0,
        timelineEventsCreated: 0 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
    
    // 准备插入的新闻数据
    const newsToInsert = newNewsItems.map(news => ({
      title: news.title,
      summary: news.summary,
      content: news.content,
      author: news.author,
      source: news.source,
      source_url: `https://example.com/news/${Date.now()}`, // 模拟URL
      published_at: new Date().toISOString(),
      category: news.category,
      tags: news.tags
    }));
    
    // 插入新闻数据
    const { data: insertedNews, error: insertError } = await supabase
      .from('ai_news')
      .insert(newsToInsert)
      .select();
    
    if (insertError) {
      console.error('插入新闻失败:', insertError);
      throw insertError;
    }
    
    console.log(`成功插入 ${insertedNews?.length || 0} 条新闻`);
    
    // 检查是否有新闻被自动转换为时间线事件
    const { data: newTimelineEvents } = await supabase
      .from('timeline_events')
      .select('*')
      .in('news_source_id', insertedNews?.map(news => news.id) || []);
    
    console.log(`自动创建了 ${newTimelineEvents?.length || 0} 个时间线事件`);
    
    return new Response(JSON.stringify({
      message: '成功获取并处理AI新闻',
      newsCount: insertedNews?.length || 0,
      timelineEventsCreated: newTimelineEvents?.length || 0,
      news: insertedNews
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
    
  } catch (error) {
    console.error('获取AI新闻时出错:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: '获取AI新闻失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
