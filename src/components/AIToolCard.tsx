
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, ExternalLink, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  userRatings: number;
  tags: string[];
  logo: string;
  website: string;
  featured: boolean;
}

interface AIToolCardProps {
  tool: AITool;
}

export const AIToolCard = ({ tool }: AIToolCardProps) => {
  const { t } = useTranslation();
  const [userRating, setUserRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);

  const handleRating = (rating: number) => {
    setUserRating(rating);
    console.log(`用户为 ${tool.name} 评分: ${rating} 星`);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      textGeneration: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      imageGeneration: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      videoGeneration: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      codeGeneration: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      dataAnalysis: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
      automation: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
      audioGeneration: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      chatbot: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
      productivity: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'
    };
    return colors[category] || colors.automation;
  };

  return (
    <Card className="group relative overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105">
      {/* Featured Badge */}
      {tool.featured && (
        <div className="absolute top-3 right-3 z-10">
          <Badge className="bg-orange-500 hover:bg-orange-600 text-white text-xs">
            <TrendingUp className="w-3 h-3 mr-1" />
            {t('tools.featured')}
          </Badge>
        </div>
      )}

      <div className="p-6">
        {/* Tool Header */}
        <div className="flex items-start space-x-4 mb-4">
          <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center overflow-hidden">
            <img 
              src={tool.logo} 
              alt={tool.name}
              className="w-8 h-8 object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder.svg';
              }}
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-1 truncate">
              {tool.name}
            </h3>
            <Badge className={`text-xs ${getCategoryColor(tool.category)}`}>
              {t(`categories.${tool.category}`)}
            </Badge>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed line-clamp-2">
          {tool.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {tool.tags.slice(0, 3).map((tag) => (
            <Badge 
              key={tag} 
              variant="secondary" 
              className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
            >
              {tag}
            </Badge>
          ))}
        </div>

        {/* Rating Display */}
        <div className="flex items-center space-x-2 mb-4">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`w-4 h-4 ${
                  star <= tool.rating
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300 dark:text-gray-600'
                }`}
              />
            ))}
          </div>
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
            {tool.rating}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({tool.userRatings.toLocaleString()} {t('tools.ratings')})
          </span>
        </div>

        {/* User Rating */}
        <div className="mb-4">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">{t('tools.rateTool')}</p>
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => handleRating(star)}
                onMouseEnter={() => setHoveredRating(star)}
                onMouseLeave={() => setHoveredRating(0)}
                className="transition-colors duration-200"
              >
                <Star
                  className={`w-5 h-5 ${
                    star <= (hoveredRating || userRating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300 dark:text-gray-600 hover:text-yellow-300'
                  }`}
                />
              </button>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <Button 
          className="w-full bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-100 dark:text-black text-white transition-colors duration-200"
          size="sm"
          onClick={() => window.open(tool.website, '_blank')}
        >
          {t('tools.visitTool')}
          <ExternalLink className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {/* Hover Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  );
};
