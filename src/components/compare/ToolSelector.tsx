
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Plus, X } from 'lucide-react';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  user_ratings_count: number;
  tags: string[];
  logo_url: string;
  website: string;
  featured: boolean;
}

interface ToolSelectorProps {
  tools: AITool[];
  selectedTools: AITool[];
  onAddTool: (tool: AITool) => void;
  onRemoveTool: (toolId: string) => void;
}

export const ToolSelector = ({ tools, selectedTools, onAddTool, onRemoveTool }: ToolSelectorProps) => {
  return (
    <div className="mb-8">
      <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
        选择工具 ({selectedTools.length}/4)
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {tools.map((tool) => (
          <Card key={tool.id} className="p-4 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <img 
                  src={tool.logo_url || '/placeholder.svg'} 
                  alt={tool.name} 
                  className="w-8 h-8 rounded object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
                <h3 className="font-medium text-gray-900 dark:text-white truncate">{tool.name}</h3>
              </div>
              <Button
                size="sm"
                variant={selectedTools.find(t => t.id === tool.id) ? "secondary" : "outline"}
                onClick={() => selectedTools.find(t => t.id === tool.id) ? onRemoveTool(tool.id) : onAddTool(tool)}
                disabled={selectedTools.length >= 4 && !selectedTools.find(t => t.id === tool.id)}
              >
                {selectedTools.find(t => t.id === tool.id) ? <X className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
              </Button>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="text-sm text-gray-600 dark:text-gray-400">{tool.rating}</span>
              <span className="text-xs text-gray-500">({tool.user_ratings_count})</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{tool.description}</p>
            <div className="flex flex-wrap gap-1">
              {tool.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
              ))}
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
