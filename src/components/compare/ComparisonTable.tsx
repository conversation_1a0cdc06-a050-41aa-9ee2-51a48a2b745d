
import { useTranslation } from 'react-i18next';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star } from 'lucide-react';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  user_ratings_count: number;
  tags: string[];
  logo_url: string;
  website: string;
  featured: boolean;
}

interface ComparisonTableProps {
  selectedTools: AITool[];
}

export const ComparisonTable = ({ selectedTools }: ComparisonTableProps) => {
  const { t } = useTranslation();

  if (selectedTools.length === 0) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">
                特性
              </th>
              {selectedTools.map((tool) => (
                <th key={tool.id} className="px-6 py-4 text-center text-sm font-medium text-gray-900 dark:text-white min-w-48">
                  <div className="flex flex-col items-center space-y-2">
                    <img 
                      src={tool.logo_url || '/placeholder.svg'} 
                      alt={tool.name} 
                      className="w-8 h-8 rounded object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder.svg';
                      }}
                    />
                    <span>{tool.name}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">评分</td>
              {selectedTools.map((tool) => (
                <td key={tool.id} className="px-6 py-4 text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium">{tool.rating}</span>
                    <span className="text-xs text-gray-500">({tool.user_ratings_count})</span>
                  </div>
                </td>
              ))}
            </tr>
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">分类</td>
              {selectedTools.map((tool) => (
                <td key={tool.id} className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">
                  <Badge variant="outline" className="text-xs">
                    {t(`categories.${tool.category}`)}
                  </Badge>
                </td>
              ))}
            </tr>
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">标签</td>
              {selectedTools.map((tool) => (
                <td key={tool.id} className="px-6 py-4">
                  <div className="flex flex-wrap justify-center gap-1">
                    {tool.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">{tag}</Badge>
                    ))}
                  </div>
                </td>
              ))}
            </tr>
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">访问</td>
              {selectedTools.map((tool) => (
                <td key={tool.id} className="px-6 py-4 text-center">
                  <Button 
                    size="sm" 
                    onClick={() => window.open(tool.website, '_blank')}
                    className="text-xs"
                  >
                    访问网站
                  </Button>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};
