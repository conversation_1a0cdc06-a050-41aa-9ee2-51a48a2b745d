
import { Button } from '@/components/ui/button';

interface TimelineLoadMoreProps {
  eventsLength: number;
  pageSize: number;
  page: number;
  onLoadMore: () => void;
}

export const TimelineLoadMore = ({ eventsLength, pageSize, page, onLoadMore }: TimelineLoadMoreProps) => {
  if (eventsLength === pageSize) {
    return (
      <div className="text-center mt-12">
        <Button 
          onClick={onLoadMore}
          variant="outline"
          size="lg"
          className="px-8 py-3"
        >
          加载更多历史事件
        </Button>
      </div>
    );
  }

  if (eventsLength < pageSize && page > 0) {
    return (
      <div className="text-center mt-12 pb-8">
        <p className="text-gray-500 dark:text-gray-400">
          已显示所有AI发展历史事件
        </p>
      </div>
    );
  }

  return null;
};
