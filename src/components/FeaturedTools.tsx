
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, TrendingUp, Clock } from 'lucide-react';

export const FeaturedTools = () => {
  const { t } = useTranslation();

  const featuredTools = [
    {
      name: 'ChatGPT',
      category: 'Text Generation',
      description: 'Advanced conversational AI by OpenAI',
      rating: 4.8,
      trending: true,
      new: false,
      image: '/placeholder.svg'
    },
    {
      name: 'Midjourney',
      category: 'Image Generation',
      description: 'AI art generator with stunning results',
      rating: 4.9,
      trending: true,
      new: false,
      image: '/placeholder.svg'
    },
    {
      name: 'Claude 3',
      category: 'Text Generation',
      description: 'Anthropic\'s latest AI assistant',
      rating: 4.7,
      trending: false,
      new: true,
      image: '/placeholder.svg'
    },
    {
      name: 'GitHub Copilot',
      category: 'Code Generation',
      description: 'AI pair programmer for developers',
      rating: 4.6,
      trending: true,
      new: false,
      image: '/placeholder.svg'
    },
    {
      name: 'Runway ML',
      category: 'Video Generation',
      description: 'Next-generation creative suite',
      rating: 4.5,
      trending: false,
      new: true,
      image: '/placeholder.svg'
    },
    {
      name: 'Stable Diffusion',
      category: 'Image Generation',
      description: 'Open-source image generation model',
      rating: 4.4,
      trending: true,
      new: false,
      image: '/placeholder.svg'
    }
  ];

  return (
    <section className="py-24 bg-white dark:bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('tools.featured')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Discover the most popular and innovative AI tools across different categories
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredTools.map((tool, index) => (
            <Card
              key={tool.name}
              className="group relative overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all duration-300 hover:shadow-2xl hover:transform hover:scale-105 cursor-pointer"
            >
              <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
                <img 
                  src={tool.image} 
                  alt={tool.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                
                {/* Badges */}
                <div className="absolute top-3 left-3 flex space-x-2">
                  {tool.trending && (
                    <Badge className="bg-red-500 hover:bg-red-600 text-white text-xs">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      {t('tools.trending')}
                    </Badge>
                  )}
                  {tool.new && (
                    <Badge className="bg-green-500 hover:bg-green-600 text-white text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {t('tools.newest')}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                      {tool.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {tool.category}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {tool.rating}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed">
                  {tool.description}
                </p>

                <Button 
                  className="w-full bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-100 dark:text-black text-white transition-colors duration-200"
                  size="sm"
                >
                  Learn More
                </Button>
              </div>

              {/* Hover Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button 
            size="lg"
            variant="outline"
            className="border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 px-8 py-3 text-lg font-medium rounded-full transition-all duration-300 hover:shadow-lg"
          >
            View All Tools
          </Button>
        </div>
      </div>
    </section>
  );
};
