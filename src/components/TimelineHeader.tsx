
import { useTranslation } from 'react-i18next';

export const TimelineHeader = () => {
  const { t } = useTranslation();

  return (
    <div className="text-center mb-12">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        {t('timeline.title')}
      </h1>
      <p className="text-xl text-gray-600 dark:text-gray-400 mb-2">
        探索AI发展的重要里程碑
      </p>
      <p className="text-gray-500 dark:text-gray-500">
        按时间倒序排列的AI发展历程，展示最新的突破和发展
      </p>
    </div>
  );
};
