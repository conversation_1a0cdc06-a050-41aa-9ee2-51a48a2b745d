
import { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface ToolRatingProps {
  toolId: string;
  currentRating: number;
  ratingsCount: number;
  onRatingUpdate?: () => void;
}

export const ToolRating = ({ toolId, currentRating, ratingsCount, onRatingUpdate }: ToolRatingProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [userRating, setUserRating] = useState<number | null>(null);
  const [hoveredRating, setHoveredRating] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      fetchUserRating();
    }
  }, [user, toolId]);

  const fetchUserRating = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_tool_ratings')
        .select('rating')
        .eq('tool_id', toolId)
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('获取用户评分失败:', error);
        return;
      }

      setUserRating(data?.rating || null);
    } catch (error) {
      console.error('获取用户评分失败:', error);
    }
  };

  const handleRating = async (rating: number) => {
    if (!user) {
      toast({
        title: "请先登录",
        description: "只有登录用户才能评分",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('user_tool_ratings')
        .upsert({
          user_id: user.id,
          tool_id: toolId,
          rating: rating
        });

      if (error) throw error;

      setUserRating(rating);
      toast({
        title: "评分成功",
        description: `您给出了 ${rating} 星评分`,
      });

      if (onRatingUpdate) {
        onRatingUpdate();
      }
    } catch (error) {
      console.error('评分失败:', error);
      toast({
        title: "评分失败",
        description: "无法保存您的评分，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300 ml-1">
            {currentRating.toFixed(1)}
          </span>
        </div>
        <span className="text-xs text-gray-500">
          ({ratingsCount} 评分)
        </span>
        <span className="text-xs text-gray-500">
          · 请登录后评分
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <div className="flex items-center">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300 ml-1">
            {currentRating.toFixed(1)}
          </span>
        </div>
        <span className="text-xs text-gray-500">
          ({ratingsCount} 评分)
        </span>
      </div>

      <div className="flex items-center space-x-1">
        <span className="text-xs text-gray-500 mr-2">您的评分:</span>
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => handleRating(star)}
            onMouseEnter={() => setHoveredRating(star)}
            onMouseLeave={() => setHoveredRating(null)}
            disabled={loading}
            className="focus:outline-none disabled:opacity-50"
          >
            <Star
              className={`w-4 h-4 transition-colors ${
                star <= (hoveredRating || userRating || 0)
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300 dark:text-gray-600'
              }`}
            />
          </button>
        ))}
        {userRating && (
          <span className="text-xs text-gray-500 ml-2">
            ({userRating} 星)
          </span>
        )}
      </div>
    </div>
  );
};
