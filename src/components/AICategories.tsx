
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/card';
import { FileText, Image, Youtube, Code, Calendar, Settings } from 'lucide-react';

export const AICategories = () => {
  const { t } = useTranslation();

  const categories = [
    {
      key: 'textGeneration',
      icon: FileText,
      gradient: 'from-blue-500 to-cyan-500',
      description: 'GPT, Claude, Gemini, and more'
    },
    {
      key: 'imageGeneration',
      icon: Image,
      gradient: 'from-purple-500 to-pink-500',
      description: 'DALL-E, Midjourney, Stable Diffusion'
    },
    {
      key: 'videoGeneration',
      icon: Youtube,
      gradient: 'from-red-500 to-orange-500',
      description: 'Sora, Runway, Pika Labs'
    },
    {
      key: 'codeGeneration',
      icon: Code,
      gradient: 'from-green-500 to-emerald-500',
      description: 'GitHub Copilot, Cursor, CodeT5'
    },
    {
      key: 'dataAnalysis',
      icon: Calendar,
      gradient: 'from-indigo-500 to-blue-500',
      description: 'Data analysis and visualization'
    },
    {
      key: 'automation',
      icon: Settings,
      gradient: 'from-gray-500 to-slate-500',
      description: 'Workflow automation tools'
    }
  ];

  return (
    <section className="py-24 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('categories.title')}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <Card
                key={category.key}
                className="group relative overflow-hidden bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-800/50 hover:border-gray-300/50 dark:hover:border-gray-700/50 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl cursor-pointer"
              >
                <div className="p-8">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${category.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    {t(`categories.${category.key}`)}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {category.description}
                  </p>
                </div>

                {/* Hover Gradient Effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${category.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};
