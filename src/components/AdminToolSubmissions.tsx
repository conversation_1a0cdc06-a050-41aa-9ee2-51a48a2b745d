
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ToolSubmission {
  id: string;
  name: string;
  category: string;
  description: string;
  website: string;
  logo_url?: string;
  tags: string[];
  status: 'pending' | 'approved' | 'rejected';
  is_anonymous: boolean;
  created_at: string;
  submitted_by?: string;
}

export const AdminToolSubmissions = () => {
  const { isSuperAdmin } = useAuth();
  const { toast } = useToast();
  const [submissions, setSubmissions] = useState<ToolSubmission[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isSuperAdmin) {
      fetchSubmissions();
    }
  }, [isSuperAdmin]);

  const fetchSubmissions = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_tool_submissions')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSubmissions((data || []) as ToolSubmission[]);
    } catch (error) {
      console.error('获取提交失败:', error);
      toast({
        title: "获取失败",
        description: "无法获取工具提交列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (submission: ToolSubmission) => {
    try {
      // 批准提交并添加到AI工具表
      const { error: toolError } = await supabase
        .from('ai_tools')
        .insert({
          name: submission.name,
          category: submission.category,
          description: submission.description,
          website: submission.website,
          logo_url: submission.logo_url,
          tags: submission.tags,
          submission_id: submission.id
        });

      if (toolError) throw toolError;

      // 更新提交状态
      const { error: updateError } = await supabase
        .from('ai_tool_submissions')
        .update({ 
          status: 'approved',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', submission.id);

      if (updateError) throw updateError;

      toast({
        title: "批准成功",
        description: `${submission.name} 已添加到AI工具列表`,
      });

      fetchSubmissions();
    } catch (error) {
      console.error('批准失败:', error);
      toast({
        title: "批准失败",
        description: "无法批准该工具提交",
        variant: "destructive",
      });
    }
  };

  const handleReject = async (submissionId: string) => {
    try {
      const { error } = await supabase
        .from('ai_tool_submissions')
        .update({ 
          status: 'rejected',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', submissionId);

      if (error) throw error;

      toast({
        title: "已拒绝",
        description: "工具提交已被拒绝",
      });

      fetchSubmissions();
    } catch (error) {
      console.error('拒绝失败:', error);
      toast({
        title: "操作失败",
        description: "无法拒绝该工具提交",
        variant: "destructive",
      });
    }
  };

  if (!isSuperAdmin) {
    return null;
  }

  if (loading) {
    return <div className="text-center py-8">加载中...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          工具提交管理
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          审核用户提交的AI工具
        </p>
      </div>

      <div className="grid gap-4">
        {submissions.map((submission) => (
          <Card key={submission.id} className="p-4">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{submission.name}</CardTitle>
                  <CardDescription className="flex items-center gap-2 mt-1">
                    <Badge variant="outline">{submission.category}</Badge>
                    {submission.is_anonymous && (
                      <Badge variant="secondary">匿名提交</Badge>
                    )}
                    <Badge 
                      variant={
                        submission.status === 'approved' ? 'default' : 
                        submission.status === 'rejected' ? 'destructive' : 
                        'secondary'
                      }
                    >
                      {submission.status === 'pending' ? '待审核' : 
                       submission.status === 'approved' ? '已批准' : '已拒绝'}
                    </Badge>
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(submission.website, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                {submission.description}
              </p>
              
              {submission.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-3">
                  {submission.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {submission.status === 'pending' && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleApprove(submission)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    批准
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleReject(submission.id)}
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    拒绝
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {submissions.length === 0 && (
        <div className="text-center py-12">
          <Clock className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
            暂无提交
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            目前没有待审核的工具提交
          </p>
        </div>
      )}
    </div>
  );
};
