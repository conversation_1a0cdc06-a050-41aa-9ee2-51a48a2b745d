
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, ExternalLink, Tag, Building2, Star, TrendingUp } from 'lucide-react';
import { TimelineEvent } from '@/types/timeline';
import { 
  getImpactLevelColor, 
  getImpactLevelText, 
  getSignificanceColor, 
  formatDateWithYear 
} from '@/utils/timelineUtils';

interface TimelineEventCardProps {
  event: TimelineEvent;
}

export const TimelineEventCard = ({ event }: TimelineEventCardProps) => {
  const { i18n } = useTranslation();
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);

  return (
    <div className="relative flex items-start">
      {/* Timeline dot with significance score */}
      <div className="relative">
        <div className={`absolute left-6 w-4 h-4 rounded-full border-4 border-white dark:border-gray-900 ${getImpactLevelColor(event.impact_level)}`}></div>
        {event.significance_score && (
          <div className="absolute left-12 -top-1 text-xs font-bold text-gray-600 dark:text-gray-400">
            {event.significance_score}
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="ml-16 w-full">
        <Card className="shadow-md hover:shadow-lg transition-shadow duration-200">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                    {event.title}
                  </CardTitle>
                  {event.significance_score && (
                    <div className="flex items-center gap-1">
                      <Star className={`w-4 h-4 ${getSignificanceColor(event.significance_score)}`} />
                      <span className={`text-sm font-bold ${getSignificanceColor(event.significance_score)}`}>
                        {event.significance_score}
                      </span>
                    </div>
                  )}
                </div>
                <CardDescription className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Calendar className="w-4 h-4" />
                  {formatDateWithYear(event.date, i18n.language)}
                </CardDescription>
              </div>
              {event.impact_level && (
                <Badge variant="secondary" className={`${getImpactLevelColor(event.impact_level)} text-white`}>
                  {getImpactLevelText(event.impact_level)}
                </Badge>
              )}
            </div>
          </CardHeader>
          
          <CardContent>
            {/* 基本描述 */}
            {event.description && (
              <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                {event.description}
              </p>
            )}
            
            {/* 详细描述（可展开） */}
            {event.detailed_description && (
              <div className="mb-4">
                {expandedEvent === event.id ? (
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
                      {event.detailed_description}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setExpandedEvent(null)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      收起详情
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setExpandedEvent(event.id)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    查看详细描述
                  </Button>
                )}
              </div>
            )}
            
            {/* 关键特性 */}
            {event.key_features && event.key_features.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  关键特性
                </h4>
                <div className="flex flex-wrap gap-1">
                  {event.key_features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs bg-blue-50 dark:bg-blue-900/20">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* 相关公司 */}
            {event.related_companies && event.related_companies.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Building2 className="w-4 h-4" />
                  相关公司
                </h4>
                <div className="flex flex-wrap gap-1">
                  {event.related_companies.map((company, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {company}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* 标签和操作 */}
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex flex-wrap gap-2">
                {event.category && (
                  <Badge variant="outline">
                    {event.category}
                  </Badge>
                )}
                {event.tags?.map((tag, tagIndex) => (
                  <Badge key={tagIndex} variant="secondary" className="text-xs">
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
              
              {event.source_url && (
                <Button variant="outline" size="sm" asChild>
                  <a 
                    href={event.source_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-2"
                  >
                    <ExternalLink className="w-4 h-4" />
                    查看详情
                  </a>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
