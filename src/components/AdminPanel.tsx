
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, Database } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { CronConfig } from '@/components/CronConfig';

export const AdminPanel = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchAINews = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('fetch-ai-news', {
        body: { manual: true }
      });

      if (error) throw error;

      toast({
        title: "新闻获取成功",
        description: `获取了 ${data.newsCount} 条新闻，创建了 ${data.timelineEventsCreated} 个时间线事件`,
      });
    } catch (error) {
      console.error('获取新闻失败:', error);
      toast({
        title: "获取新闻失败",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            AI新闻管理面板
          </CardTitle>
          <CardDescription>
            手动触发AI新闻获取和管理自动任务
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={fetchAINews}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                正在获取新闻...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                手动获取最新AI新闻
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <CronConfig />
    </div>
  );
};
