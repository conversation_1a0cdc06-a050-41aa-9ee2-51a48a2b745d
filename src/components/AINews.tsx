
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Calendar, User, Tag } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';

interface AINewsItem {
  id: string;
  title: string;
  summary: string | null;
  content: string | null;
  author: string | null;
  source: string | null;
  source_url: string;
  image_url: string | null;
  published_at: string;
  tags: string[] | null;
  category: string | null;
}

export const AINews = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(0);
  const pageSize = 12;

  const { data: news, isLoading, error } = useQuery({
    queryKey: ['ai-news', page],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('ai_news')
        .select('*')
        .order('published_at', { ascending: false })
        .range(page * pageSize, (page + 1) * pageSize - 1);
      
      if (error) throw error;
      return data as AINewsItem[];
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <p className="text-red-600">{t('news.noNews')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('news.title')}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-2">
            {t('news.subtitle')}
          </p>
          <p className="text-gray-500 dark:text-gray-500">
            {t('news.description')}
          </p>
        </div>

        {/* News Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news?.map((article) => (
            <Card key={article.id} className="h-full flex flex-col shadow-md hover:shadow-lg transition-shadow duration-200">
              {article.image_url && (
                <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg overflow-hidden">
                  <img 
                    src={article.image_url} 
                    alt={article.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              <CardHeader className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  {article.category && (
                    <Badge variant="secondary" className="mb-2">
                      {article.category}
                    </Badge>
                  )}
                </div>
                
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                  {article.title}
                </CardTitle>
                
                <CardDescription className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {format(new Date(article.published_at), 'PP', { 
                      locale: i18n.language === 'zh' ? zhCN : enUS 
                    })}
                  </span>
                  {article.author && (
                    <span className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {article.author}
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col">
                {article.summary && (
                  <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3 flex-1">
                    {article.summary}
                  </p>
                )}
                
                {article.tags && article.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-4">
                    {article.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                    {article.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{article.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
                
                <div className="flex items-center justify-between mt-auto">
                  {article.source && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {t('news.source')}: {article.source}
                    </span>
                  )}
                  
                  <Button variant="outline" size="sm" asChild>
                    <a 
                      href={article.source_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                    >
                      <ExternalLink className="w-4 h-4" />
                      {t('news.readMore')}
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More Button */}
        {news && news.length === pageSize && (
          <div className="text-center mt-12">
            <Button 
              onClick={() => setPage(prev => prev + 1)}
              variant="outline"
              size="lg"
            >
              {t('news.loadMore')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
