
import { useTranslation } from 'react-i18next';
import { Star } from 'lucide-react';

export const Footer = () => {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-900 dark:bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">AI Hub</span>
            </div>
            <p className="text-gray-400 max-w-md leading-relaxed">
              {t('footer.description')}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.quickLinks')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">{t('nav.tools')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('nav.timeline')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('nav.compare')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('nav.news')}</a></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.categories')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">{t('categories.textGeneration')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('categories.imageGeneration')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('categories.videoGeneration')}</a></li>
              <li><a href="#" className="hover:text-white transition-colors">{t('categories.codeGeneration')}</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 AI Hub. {t('footer.rights')}</p>
        </div>
      </div>
    </footer>
  );
};
