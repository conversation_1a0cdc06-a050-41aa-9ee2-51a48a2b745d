
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Filter, Search, MessageSquare, Image, Video, Code, BarChart3, Zap, Volume2, Bot, Briefcase, ExternalLink } from 'lucide-react';
import { ToolRating } from '@/components/ToolRating';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  user_ratings_count: number;
  tags: string[];
  logo_url: string;
  website: string;
  featured: boolean;
}

// Category mapping for translation
const categoryMapping: { [key: string]: string } = {
  'textGeneration': 'textGeneration',
  'imageGeneration': 'imageGeneration', 
  'videoGeneration': 'videoGeneration',
  'codeGeneration': 'codeGeneration',
  'dataAnalysis': 'dataAnalysis',
  'automation': 'automation',
  'audioGeneration': 'audioGeneration',
  'chatbot': 'chatbot',
  'productivity': 'productivity',
  'design': 'design',
  'marketing': 'marketing',
  'research': 'research'
};

export const AIToolsNavigation = () => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [tools, setTools] = useState<AITool[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTools();
  }, []);

  const fetchTools = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_tools')
        .select('*')
        .order('featured', { ascending: false })
        .order('rating', { ascending: false });

      if (error) throw error;
      setTools(data || []);
    } catch (error) {
      console.error('获取AI工具失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesCategory = selectedCategory === 'all' || tool.category === selectedCategory;
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Get translated category name
  const getTranslatedCategory = (categoryKey: string) => {
    const mappedKey = categoryMapping[categoryKey] || categoryKey;
    return t(`categories.${mappedKey}`);
  };

  // 动态计算每个分类的工具数量
  const getCategoryCount = (categoryKey: string) => {
    if (categoryKey === 'all') return tools.length;
    return tools.filter(tool => tool.category === categoryKey).length;
  };

  // 定义分类图标映射
  const getCategoryIcon = (categoryKey: string) => {
    const iconMap = {
      'all': Star,
      'textGeneration': MessageSquare,
      'imageGeneration': Image,
      'videoGeneration': Video,
      'codeGeneration': Code,
      'dataAnalysis': BarChart3,
      'automation': Zap,
      'audioGeneration': Volume2,
      'chatbot': Bot,
      'productivity': Briefcase
    };
    return iconMap[categoryKey as keyof typeof iconMap] || Star;
  };

  const categories = [
    { key: 'all', count: getCategoryCount('all') },
    { key: 'textGeneration', count: getCategoryCount('textGeneration') },
    { key: 'imageGeneration', count: getCategoryCount('imageGeneration') },
    { key: 'videoGeneration', count: getCategoryCount('videoGeneration') },
    { key: 'codeGeneration', count: getCategoryCount('codeGeneration') },
    { key: 'dataAnalysis', count: getCategoryCount('dataAnalysis') },
    { key: 'automation', count: getCategoryCount('automation') },
    { key: 'audioGeneration', count: getCategoryCount('audioGeneration') },
    { key: 'chatbot', count: getCategoryCount('chatbot') },
    { key: 'productivity', count: getCategoryCount('productivity') }
  ];

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">{t('loading') || '加载中...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          {t('hero.title')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          {t('hero.description')}
        </p>
      </div>

      {/* Search and Filter */}
      <div className="mb-8 space-y-6">
        {/* Search Bar */}
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder={t('tools.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => {
            const IconComponent = getCategoryIcon(category.key);
            return (
              <Button
                key={category.key}
                variant={selectedCategory === category.key ? "default" : "outline"}
                onClick={() => setSelectedCategory(category.key)}
                className={`rounded-full px-6 py-2 transition-all duration-300 flex items-center space-x-2 ${
                  selectedCategory === category.key
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                <IconComponent className="w-4 h-4" />
                <span>{t(`categories.${category.key}`)}</span>
                <Badge variant="secondary" className="ml-2 text-xs">
                  {category.count}
                </Badge>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTools.map((tool) => (
          <Card
            key={tool.id}
            className="group relative overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all duration-300 hover:shadow-2xl hover:transform hover:scale-105 cursor-pointer"
          >
            <div className="p-6">
              {/* Tool Header with Logo */}
              <div className="flex items-start space-x-4 mb-4">
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center overflow-hidden flex-shrink-0">
                  <img 
                    src={tool.logo_url || '/placeholder.svg'} 
                    alt={tool.name}
                    className="w-8 h-8 object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder.svg';
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                        {tool.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getTranslatedCategory(tool.category)}
                      </p>
                    </div>
                    {tool.featured && (
                      <Badge className="bg-red-500 hover:bg-red-600 text-white text-xs flex-shrink-0">
                        <Star className="w-3 h-3 mr-1" />
                        {t('tools.featured') || '推荐'}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed">
                {tool.description}
              </p>

              {tool.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {tool.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="mb-4">
                <ToolRating
                  toolId={tool.id}
                  currentRating={tool.rating || 0}
                  ratingsCount={tool.user_ratings_count || 0}
                  onRatingUpdate={fetchTools}
                />
              </div>

              <Button 
                className="w-full bg-gray-900 hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-100 dark:text-black text-white transition-colors duration-200"
                size="sm"
                onClick={() => window.open(tool.website, '_blank')}
              >
                {t('tools.visitTool') || '访问工具'}
                <ExternalLink className="w-4 h-4 ml-2" />
              </Button>
            </div>

            {/* Hover Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </Card>
        ))}
      </div>

      {/* No Results */}
      {filteredTools.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Filter className="w-16 h-16 mx-auto mb-4" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
            {t('tools.noResults')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('tools.noResultsDesc')}
          </p>
        </div>
      )}
    </div>
  );
};
