
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Clock, Save, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CronConfig {
  id: string;
  function_name: string;
  cron_expression: string;
  is_enabled: boolean;
  description: string;
}

export const CronConfig = () => {
  const [config, setConfig] = useState<CronConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('cron_config')
        .select('*')
        .eq('function_name', 'fetch-ai-news')
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setConfig(data);
      }
    } catch (error) {
      console.error('获取cron配置失败:', error);
      toast({
        title: "获取配置失败",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfig = async () => {
    if (!config) return;

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('cron_config')
        .upsert({
          function_name: 'fetch-ai-news',
          cron_expression: config.cron_expression,
          is_enabled: config.is_enabled,
          description: config.description,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast({
        title: "配置保存成功",
        description: "Cron表达式配置已更新",
      });
    } catch (error) {
      console.error('保存cron配置失败:', error);
      toast({
        title: "保存配置失败",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const validateCronExpression = (expression: string) => {
    // 简单的cron表达式验证（5个字段）
    const cronRegex = /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/;
    return cronRegex.test(expression);
  };

  const getCronDescription = (expression: string) => {
    const commonExpressions: { [key: string]: string } = {
      '0 * * * *': '每小时执行',
      '0 */2 * * *': '每2小时执行',
      '0 */6 * * *': '每6小时执行',
      '0 0 * * *': '每天午夜执行',
      '0 9 * * *': '每天上午9点执行',
      '0 */30 * * *': '每30分钟执行',
      '*/15 * * * *': '每15分钟执行'
    };
    return commonExpressions[expression] || '自定义表达式';
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">加载配置中...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          定时任务配置
        </CardTitle>
        <CardDescription>
          配置AI新闻自动获取任务的执行时间
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {config && (
          <>
            <div className="space-y-2">
              <Label htmlFor="cron-expression">Cron表达式</Label>
              <Input
                id="cron-expression"
                value={config.cron_expression}
                onChange={(e) => setConfig({
                  ...config,
                  cron_expression: e.target.value
                })}
                placeholder="0 * * * *"
                className={!validateCronExpression(config.cron_expression) ? 'border-red-500' : ''}
              />
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <AlertCircle className="w-4 h-4" />
                {validateCronExpression(config.cron_expression) ? (
                  <span className="text-green-600">
                    {getCronDescription(config.cron_expression)}
                  </span>
                ) : (
                  <span className="text-red-600">
                    无效的Cron表达式格式
                  </span>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">任务描述</Label>
              <Input
                id="description"
                value={config.description}
                onChange={(e) => setConfig({
                  ...config,
                  description: e.target.value
                })}
                placeholder="任务描述"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="enabled"
                checked={config.is_enabled}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  is_enabled: checked
                })}
              />
              <Label htmlFor="enabled">启用定时任务</Label>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium mb-2">常用Cron表达式示例：</h4>
              <div className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
                <div><code>0 * * * *</code> - 每小时执行</div>
                <div><code>0 */2 * * *</code> - 每2小时执行</div>
                <div><code>0 */6 * * *</code> - 每6小时执行</div>
                <div><code>0 0 * * *</code> - 每天午夜执行</div>
                <div><code>*/30 * * * *</code> - 每30分钟执行</div>
              </div>
            </div>

            <Button
              onClick={saveConfig}
              disabled={isSaving || !validateCronExpression(config.cron_expression)}
              className="w-full"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  保存配置
                </>
              )}
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
};
