
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { TimelineHeader } from './TimelineHeader';
import { TimelineEventCard } from './TimelineEventCard';
import { TimelineLoadMore } from './TimelineLoadMore';
import { TimelineEvent } from '@/types/timeline';

export const Timeline = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const pageSize = 10;

  // 获取当前时间之前的所有时间线事件，按时间倒序排序（最新的在前面）
  const { data: events, isLoading, error } = useQuery({
    queryKey: ['timeline-events', page],
    queryFn: async () => {
      const currentDate = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('timeline_events')
        .select('*')
        .lte('date', currentDate)
        .order('date', { ascending: false }) // 按日期倒序
        .order('significance_score', { ascending: false }) // 同日期的按重要性倒序
        .range(page * pageSize, (page + 1) * pageSize - 1);
      
      if (error) throw error;
      return data as TimelineEvent[];
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">正在加载AI发展时间线...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <p className="text-red-600">{t('timeline.noEvents')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto pb-24">
        <TimelineHeader />

        {/* Timeline */}
        <div className="relative">
          {/* Vertical line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-blue-200 dark:bg-blue-800"></div>
          
          <div className="space-y-8">
            {events?.map((event) => (
              <TimelineEventCard key={event.id} event={event} />
            ))}
          </div>
        </div>

        <TimelineLoadMore 
          eventsLength={events?.length || 0}
          pageSize={pageSize}
          page={page}
          onLoadMore={() => setPage(prev => prev + 1)}
        />
      </div>
    </div>
  );
};
