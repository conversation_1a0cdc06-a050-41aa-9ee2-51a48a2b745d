
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  user_ratings_count: number;
  tags: string[];
  logo_url: string;
  website: string;
  featured: boolean;
}

export const useAITools = () => {
  const [tools, setTools] = useState<AITool[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchTools = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_tools')
        .select('*')
        .order('featured', { ascending: false })
        .order('rating', { ascending: false });

      if (error) throw error;
      setTools(data || []);
    } catch (error) {
      console.error('获取AI工具失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTools();
  }, []);

  return { tools, loading, fetchTools };
};
