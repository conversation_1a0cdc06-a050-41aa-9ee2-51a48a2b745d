
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      loading: 'Loading...',
      nav: {
        home: 'Home',
        tools: 'AI Tools',
        timeline: 'AI Timeline',
        compare: 'Tool Comparison',
        news: 'AI News',
        submit: 'Submit Tool'
      },
      hero: {
        title: 'AI Tool Navigation Center',
        subtitle: 'Discover the latest and most comprehensive AI tools',
        description: 'Categorized by domain, user-rated to help you find the most suitable AI solution',
        explore: 'Explore AI Tools',
        learnMore: 'Learn More'
      },
      categories: {
        all: 'All',
        textGeneration: 'Text Generation',
        imageGeneration: 'Image Generation',
        videoGeneration: 'Video Generation',
        codeGeneration: 'Code Generation',
        dataAnalysis: 'Data Analysis',
        automation: 'Automation',
        audioGeneration: 'Audio Generation',
        chatbot: 'Chatbot',
        productivity: 'Productivity',
        design: 'Design',
        marketing: 'Marketing',
        research: 'Research'
      },
      tools: {
        featured: 'Featured',
        trending: 'Trending',
        newest: 'New',
        search: 'Search AI tools...',
        loadMore: 'Load More Tools',
        noResults: 'No tools found',
        noResultsDesc: 'Try adjusting your search criteria or selecting other categories',
        visitTool: 'Visit Tool',
        rateTool: 'Rate this tool:',
        ratings: 'ratings',
        totalTools: 'tools found',
        filterBy: 'Filter by category',
        sortBy: 'Sort by',
        popularity: 'Popularity',
        rating: 'Rating',
        alphabetical: 'Alphabetical'
      },
      timeline: {
        title: 'AI Development Timeline',
        subtitle: 'Track the evolution of artificial intelligence',
        description: 'Explore major milestones and breakthroughs in AI development',
        loadMore: 'Load More Events',
        noEvents: 'No timeline events found',
        year: 'Year',
        month: 'Month',
        day: 'Day'
      },
      news: {
        title: 'Latest AI News',
        subtitle: 'Stay updated with the latest developments',
        description: 'Fresh insights and breaking news from the AI world',
        readMore: 'Read More',
        source: 'Source',
        publishedOn: 'Published on',
        noNews: 'No news articles found',
        loadMore: 'Load More Articles'
      },
      footer: {
        description: 'Your comprehensive guide to AI tools across all industries. Track the evolution, compare features, and never miss an update.',
        quickLinks: 'Quick Links',
        categories: 'Categories',
        rights: 'All rights reserved.'
      }
    }
  },
  zh: {
    translation: {
      loading: '加载中...',
      nav: {
        home: '首页',
        tools: 'AI工具',
        timeline: 'AI时间线',
        compare: '工具对比',
        news: 'AI资讯',
        submit: '提交工具'
      },
      hero: {
        title: 'AI工具导航中心',
        subtitle: '发现最新最全的AI工具',
        description: '按领域分类，用户评分，助您找到最适合的AI解决方案',
        explore: '探索AI工具',
        learnMore: '了解更多'
      },
      categories: {
        all: '全部',
        textGeneration: '文本生成',
        imageGeneration: '图像生成',
        videoGeneration: '视频生成',
        codeGeneration: '代码生成',
        dataAnalysis: '数据分析',
        automation: '自动化',
        audioGeneration: '音频生成',
        chatbot: '聊天机器人',
        productivity: '生产力工具',
        design: '设计工具',
        marketing: '营销工具',
        research: '研究工具'
      },
      tools: {
        featured: '推荐',
        trending: '热门',
        newest: '最新',
        search: '搜索AI工具...',
        loadMore: '加载更多工具',
        noResults: '未找到相关工具',
        noResultsDesc: '尝试调整搜索条件或选择其他分类',
        visitTool: '访问工具',
        rateTool: '为此工具评分:',
        ratings: '评价',
        totalTools: '个工具',
        filterBy: '按分类筛选',
        sortBy: '排序方式',
        popularity: '热门度',
        rating: '评分',
        alphabetical: '字母顺序'
      },
      timeline: {
        title: 'AI发展时间线',
        subtitle: '追踪人工智能的演进历程',
        description: '探索AI发展中的重要里程碑和突破性进展',
        loadMore: '加载更多事件',
        noEvents: '未找到时间线事件',
        year: '年',
        month: '月',
        day: '日'
      },
      news: {
        title: '最新AI资讯',
        subtitle: '掌握最新发展动态',
        description: '来自AI世界的最新见解和突发新闻',
        readMore: '阅读更多',
        source: '来源',
        publishedOn: '发布于',
        noNews: '未找到新闻文章',
        loadMore: '加载更多文章'
      },
      footer: {
        description: '您的AI工具综合指南。跟踪演进过程，对比功能特性，永不错过任何更新。',
        quickLinks: '快速链接',
        categories: '分类',
        rights: '版权所有。'
      }
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
