// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lorfybzhoawbenxxkleg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxvcmZ5Ynpob2F3YmVueHhrbGVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMTUwNTksImV4cCI6MjA2NDU5MTA1OX0.xytkU_qgcErFCNNRd__-ywTv9lEC9xtds3IwUPIPXj4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);