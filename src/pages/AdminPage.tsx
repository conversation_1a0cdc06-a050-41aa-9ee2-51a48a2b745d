
import { ThemeProvider } from '@/providers/ThemeProvider';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { AdminPanel } from '@/components/AdminPanel';
import { AdminToolSubmissions } from '@/components/AdminToolSubmissions';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const AdminPage = () => {
  const { isSuperAdmin, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  if (!isSuperAdmin) {
    return <Navigate to="/" replace />;
  }

  return (
    <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
      <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
        <Navbar />
        <main className="pt-24 px-4 sm:px-6 lg:px-8 pb-16">
          <div className="max-w-7xl mx-auto space-y-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                管理面板
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                超级管理员控制台
              </p>
            </div>
            
            <AdminPanel />
            <AdminToolSubmissions />
          </div>
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
};

export default AdminPage;
