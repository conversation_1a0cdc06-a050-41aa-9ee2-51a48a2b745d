
import { useEffect } from 'react';
import '../i18n';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { Navbar } from '@/components/Navbar';
import { AIToolsNavigation } from '@/components/AIToolsNavigation';
import { Footer } from '@/components/Footer';

const Index = () => {
  useEffect(() => {
    // Set default theme class
    document.documentElement.classList.add('light');
  }, []);

  return (
    <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
      <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
        <Navbar />
        <main className="pt-16">
          <AIToolsNavigation />
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
};

export default Index;
