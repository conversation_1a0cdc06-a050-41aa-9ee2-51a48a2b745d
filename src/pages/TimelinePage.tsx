
import { useEffect } from 'react';
import '../i18n';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { Navbar } from '@/components/Navbar';
import { Timeline } from '@/components/Timeline';
import { Footer } from '@/components/Footer';

const TimelinePage = () => {
  useEffect(() => {
    // Set default theme class
    document.documentElement.classList.add('light');
  }, []);

  return (
    <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
      <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
        <Navbar />
        <main>
          <Timeline />
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
};

export default TimelinePage;
