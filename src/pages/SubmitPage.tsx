
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Plus, X, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const SubmitPage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    website: '',
    description: '',
    category: '',
    pricing: '',
    features: '',
    contactEmail: '',
    additionalInfo: ''
  });
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { key: 'textGeneration', label: '文本生成' },
    { key: 'imageGeneration', label: '图像生成' },
    { key: 'videoGeneration', label: '视频生成' },
    { key: 'codeGeneration', label: '代码生成' },
    { key: 'dataAnalysis', label: '数据分析' },
    { key: 'automation', label: '自动化' },
    { key: 'audioGeneration', label: '音频生成' },
    { key: 'chatbot', label: '聊天机器人' },
    { key: 'productivity', label: '生产力' }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim()) && tags.length < 10) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // 模拟提交过程
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "提交成功！",
        description: "您的AI工具已提交审核，我们会在24小时内联系您。",
      });

      // 重置表单
      setFormData({
        name: '',
        website: '',
        description: '',
        category: '',
        pricing: '',
        features: '',
        contactEmail: '',
        additionalInfo: ''
      });
      setTags([]);
    } catch (error) {
      toast({
        title: "提交失败",
        description: "请稍后重试或联系我们的支持团队。",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
      <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
        <Navbar />
        <main className="pt-24 px-4 sm:px-6 lg:px-8 pb-16">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                提交AI工具
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                向我们的平台提交您的AI工具，让更多用户发现和使用您的产品
              </p>
            </div>

            <Card className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 基本信息 */}
                <div className="space-y-4">
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">基本信息</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        工具名称 *
                      </label>
                      <Input
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="请输入AI工具名称"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        官方网站 *
                      </label>
                      <Input
                        type="url"
                        value={formData.website}
                        onChange={(e) => handleInputChange('website', e.target.value)}
                        placeholder="https://example.com"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      工具描述 *
                    </label>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="详细描述您的AI工具的功能和特点..."
                      rows={4}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        工具类别 *
                      </label>
                      <select
                        value={formData.category}
                        onChange={(e) => handleInputChange('category', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        required
                      >
                        <option value="">选择类别</option>
                        {categories.map((category) => (
                          <option key={category.key} value={category.key}>
                            {category.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        定价模式 *
                      </label>
                      <Input
                        value={formData.pricing}
                        onChange={(e) => handleInputChange('pricing', e.target.value)}
                        placeholder="例如：免费 / $10/月"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* 详细信息 */}
                <div className="space-y-4">
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">详细信息</h2>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      主要功能
                    </label>
                    <Textarea
                      value={formData.features}
                      onChange={(e) => handleInputChange('features', e.target.value)}
                      placeholder="列举工具的主要功能和特性，每行一个..."
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      标签
                    </label>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        placeholder="添加标签..."
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addTag}
                        disabled={!currentTag.trim() || tags.length >= 10}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">最多添加10个标签</p>
                  </div>
                </div>

                {/* 联系信息 */}
                <div className="space-y-4">
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">联系信息</h2>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      联系邮箱 *
                    </label>
                    <Input
                      type="email"
                      value={formData.contactEmail}
                      onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      其他信息
                    </label>
                    <Textarea
                      value={formData.additionalInfo}
                      onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                      placeholder="任何其他您想要补充的信息..."
                      rows={3}
                    />
                  </div>
                </div>

                {/* 提交按钮 */}
                <div className="flex justify-center pt-6">
                  <Button
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                    className="px-8 py-3"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>提交中...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Upload className="w-4 h-4" />
                        <span>提交工具</span>
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </Card>

            {/* 提交须知 */}
            <Card className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                提交须知
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <li>• 我们会在24小时内审核您提交的工具</li>
                <li>• 请确保提供的信息准确完整</li>
                <li>• 工具必须是正常运行且可访问的</li>
                <li>• 我们保留拒绝任何不符合标准的工具的权利</li>
                <li>• 审核通过后，工具将在平台上公开展示</li>
              </ul>
            </Card>
          </div>
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
};

export default SubmitPage;
