
import { useState } from 'react';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { CompareHeader } from '@/components/compare/CompareHeader';
import { ToolSelector } from '@/components/compare/ToolSelector';
import { ComparisonTable } from '@/components/compare/ComparisonTable';
import { EmptyComparison } from '@/components/compare/EmptyComparison';
import { useAITools } from '@/hooks/useAITools';

interface AITool {
  id: string;
  name: string;
  category: string;
  description: string;
  rating: number;
  user_ratings_count: number;
  tags: string[];
  logo_url: string;
  website: string;
  featured: boolean;
}

const ComparePage = () => {
  const [selectedTools, setSelectedTools] = useState<AITool[]>([]);
  const { tools, loading } = useAITools();

  const addTool = (tool: AITool) => {
    if (selectedTools.length < 4 && !selectedTools.find(t => t.id === tool.id)) {
      setSelectedTools([...selectedTools, tool]);
    }
  };

  const removeTool = (toolId: string) => {
    setSelectedTools(selectedTools.filter(t => t.id !== toolId));
  };

  if (loading) {
    return (
      <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
        <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
          <Navbar />
          <main className="pt-24 px-4 sm:px-6 lg:px-8 pb-16">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">加载中...</p>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider defaultTheme="light" storageKey="ai-hub-theme">
      <div className="min-h-screen bg-white dark:bg-black transition-colors duration-300">
        <Navbar />
        <main className="pt-24 px-4 sm:px-6 lg:px-8 pb-16">
          <div className="max-w-7xl mx-auto">
            <CompareHeader />
            
            <ToolSelector
              tools={tools}
              selectedTools={selectedTools}
              onAddTool={addTool}
              onRemoveTool={removeTool}
            />

            {selectedTools.length > 0 ? (
              <ComparisonTable selectedTools={selectedTools} />
            ) : (
              <EmptyComparison />
            )}
          </div>
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
};

export default ComparePage;
