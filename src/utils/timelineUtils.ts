
import { format } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';

export const getImpactLevelColor = (level: string | null) => {
  switch (level) {
    case 'revolutionary': return 'bg-red-500';
    case 'high': return 'bg-orange-500';
    case 'medium': return 'bg-yellow-500';
    case 'low': return 'bg-green-500';
    default: return 'bg-gray-500';
  }
};

export const getImpactLevelText = (level: string | null) => {
  switch (level) {
    case 'revolutionary': return '革命性';
    case 'high': return '高影响';
    case 'medium': return '中等影响';
    case 'low': return '低影响';
    default: return '未知';
  }
};

export const getSignificanceColor = (score: number | null) => {
  if (!score) return 'text-gray-500';
  if (score >= 80) return 'text-red-500';
  if (score >= 60) return 'text-orange-500';
  if (score >= 40) return 'text-yellow-600';
  return 'text-green-500';
};

export const formatDateWithYear = (dateString: string, language: string) => {
  const date = new Date(dateString);
  const locale = language === 'zh' ? zhCN : enUS;
  return format(date, 'yyyy年MM月dd日', { locale });
};
